{"name": "note-by", "version": "0.1.2", "description": "An Note application built with React, TypeScript, and Electron", "main": "./out/main/index.js", "author": "https://github.com/funkpopo", "homepage": "https://github.com/funkpopo/note-by", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps && npx electron-rebuild -f -w better-sqlite3", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/provider": "^1.1.3", "@blocknote/code-block": "^0.33.0", "@blocknote/core": "^0.33.0", "@blocknote/mantine": "^0.33.0", "@blocknote/react": "^0.33.0", "@blocknote/xl-ai": "^0.33.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@douyinfe/semi-icons": "^2.82.0", "@douyinfe/semi-icons-lab": "^2.82.0", "@douyinfe/semi-ui": "^2.82.0", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@types/react-window": "^1.8.8", "@xyflow/react": "^12.6.4", "axios": "^1.10.0", "better-sqlite3": "^12.2.0", "docx": "^9.5.1", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "electron-updater": "^6.6.2", "html-to-image": "^1.11.13", "lru-cache": "^11.1.0", "md-to-pdf": "^5.2.4", "openai": "^5.8.2", "react-markdown": "^10.1.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "showdown": "^2.1.0", "unified": "^11.0.5", "uuid": "^11.1.0", "webdav": "^5.8.0", "zustand": "^5.0.5"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/better-sqlite3": "^7.6.13", "@types/echarts": "^4.9.22", "@types/node": "^22.13.13", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "electron": "^36.3.0", "electron-builder": "^26.0.13", "electron-vite": "^3.1.0", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "sass-embedded": "^1.87.0", "typescript": "^5.8.3", "vite": "^6.3.5"}}