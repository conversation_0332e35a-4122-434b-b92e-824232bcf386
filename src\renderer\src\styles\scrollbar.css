/* 自定义滚动条样式 */

/* 浅色主题滚动条 */
body[theme-mode='light'] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

body[theme-mode='light'] ::-webkit-scrollbar-track {
  background: var(--semi-color-fill-0);
  border-radius: 4px;
}

body[theme-mode='light'] ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

body[theme-mode='light'] ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.35);
}

/* 深色主题滚动条 */
body[theme-mode='dark'] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

body[theme-mode='dark'] ::-webkit-scrollbar-track {
  background: var(--semi-color-fill-0);
  border-radius: 4px;
}

body[theme-mode='dark'] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

body[theme-mode='dark'] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.35);
}

/* 设置页面滚动容器样式 */
.settings-scroll-container {
  /* 修改为更灵活的高度计算，支持小窗口显示 */
  max-height: calc(100vh - 160px);
  min-height: 200px; /* 确保小窗口时也有最小高度 */
  overflow-y: auto;
  padding-right: 8px; /* 给滚动条预留空间 */
  margin-bottom: 20px; /* 底部边距 */
  padding-bottom: 30px; /* 增加底部内边距确保内容不被截断 */
}

/* WebDAV设置特殊处理 */
.settings-scroll-container.webdav-settings {
  /* 继承所有滚动容器样式，但移除右内边距 */
  padding-right: 0; /* 移除右内边距，避免与父容器的padding-right重叠 */
  height: 100%; /* 确保高度充满Tab内容区域 */
}

/* 确保嵌套容器不产生额外滚动条 */
.settings-scroll-container .semi-tabs-content {
  overflow: visible;
}
