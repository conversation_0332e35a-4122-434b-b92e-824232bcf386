appId: com.noteby.app
productName: note-by
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!**/node_modules/fsevents/**'
  - '!**/node_modules/.bin/fsevents*'
  - '!**/fsevents/**'
  - from: '.'
    filter:
      - '**/*'
      - '!**/fsevents'
      - '!**/fsevents/**'
asarUnpack:
  - resources/**
win:
  executableName: note-by
  icon: resources/icon.png
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  icon: resources/icon.png
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: https://github.com/funkpopo
  category: Utility
  icon: resources/icon.png
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
buildDependenciesFromSource: false
