.editor-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--semi-color-bg-0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  z-index: 10;
  height: auto;
  min-height: 20px;
  flex-wrap: nowrap;
  row-gap: 10px;
}

.editor-title-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  row-gap: 5px;
  flex-shrink: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.editor-title-container .semi-typography {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.editor-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: nowrap;
}

/* 添加模型选择下拉菜单的样式 */
.editor-actions .semi-select {
  margin-right: 8px;
}

.editor-actions .semi-button {
  margin-left: 8px;
}

/* Semi Space组件样式调整，保证在窗口宽度减小时保持同一行 */
.editor-actions.semi-space {
  flex-wrap: nowrap !important;
}

.editor-actions.semi-space.semi-space-align-center {
  align-items: center !important;
}

.editor-actions.semi-space.semi-space-horizontal {
  flex-direction: row !important;
}

.editor-actions.semi-space.semi-space-tight-horizontal,
.editor-actions.semi-space.semi-space-tight-vertical {
  gap: 4px !important; /* 调整紧凑间距 */
}

/* 在暗色模式下调整样式 - 使用theme-mode属性 */
body[theme-mode='dark'] .editor-header {
  background-color: var(--semi-color-bg-1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 导出按钮组样式 */
.export-button-group {
  display: inline-flex;
  align-items: center;
}

.export-button-group .export-more-button {
  min-width: 32px;
  margin-left: -1px; /* 使按钮连接在一起 */
}

/* 当宽度小于600px时，对按钮和标题做调整 */
@media (max-width: 600px) {
  .editor-header {
    padding: 12px 16px;
  }

  .editor-title-container .semi-typography {
    font-size: 18px !important; /* 减小标题字体大小 */
    max-width: 200px; /* 限制标题最大宽度 */
  }

  .editor-actions .semi-button {
    padding: 0 8px; /* 减小按钮内边距 */
  }

  /* 在小屏幕上调整下拉菜单宽度 */
  .editor-actions .semi-select {
    width: 120px !important;
  }

  /* 调整导出按钮组在小屏幕的显示 */
  .export-button-group .semi-button {
    padding: 0 6px;
  }

  .export-button-group .semi-button > span:not(.semi-icon) {
    font-size: 12px;
  }
}

/* 当宽度非常小时，仅显示图标按钮 */
@media (max-width: 480px) {
  .editor-actions .semi-button-content > span:not(.semi-icon) {
    display: none; /* 隐藏按钮文本，只显示图标 */
  }

  .editor-actions .semi-button {
    min-width: 28px;
    width: 28px;
    padding: 0;
  }

  /* 在极小屏幕上进一步调整下拉菜单 */
  .editor-actions .semi-select {
    width: 100px !important;
  }

  /* 调整导出按钮组在超小屏幕上的显示 */
  .export-button-group .semi-button > span:not(.semi-icon) {
    display: none; /* 隐藏文本，只保留图标 */
  }

  .export-button-group .semi-button {
    min-width: 32px;
    width: 32px;
    padding: 0;
  }

  .export-button-group .export-more-button {
    min-width: 26px;
    width: 26px;
  }
}

.editor-container {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--semi-color-bg-1);
}

.editor-title-container {
  display: flex;
  align-items: center;
  flex: 1;
}

/* BlockNote Editor Theming */
.bn-container[data-color-scheme='light'] {
  /* Editor colors */
  --bn-colors-editor-text: var(--semi-color-text-0);
  --bn-colors-editor-background: var(--semi-color-bg-0);
  --bn-colors-menu-text: var(--semi-color-text-0);
  --bn-colors-menu-background: var(--semi-color-bg-0);
  --bn-colors-tooltip-text: var(--semi-color-text-0);
  --bn-colors-tooltip-background: var(--semi-color-bg-1);

  /* Interactive states */
  --bn-colors-hovered-text: var(--semi-color-text-0);
  --bn-colors-hovered-background: var(--semi-color-primary-light-default);
  --bn-colors-selected-text: #ffffff;
  --bn-colors-selected-background: var(--semi-color-primary);
  --bn-colors-disabled-text: var(--semi-color-disabled-text);
  --bn-colors-disabled-background: var(--semi-color-disabled-bg);

  /* UI elements */
  --bn-colors-shadow: var(--semi-color-shadow);
  --bn-colors-border: var(--semi-color-border);
  --bn-colors-side-menu: #cfcfcf;

  /* Other settings */
  --bn-border-radius: 6px;
}

.bn-container[data-color-scheme='dark'] {
  /* Editor colors */
  --bn-colors-editor-text: var(--semi-color-text-0);
  --bn-colors-editor-background: var(--semi-color-bg-0);
  --bn-colors-menu-text: var(--semi-color-text-0);
  --bn-colors-menu-background: var(--semi-color-bg-0);
  --bn-colors-tooltip-text: var(--semi-color-text-0);
  --bn-colors-tooltip-background: var(--semi-color-bg-1);

  /* Interactive states */
  --bn-colors-hovered-text: var(--semi-color-text-0);
  --bn-colors-hovered-background: var(--semi-color-primary-light-default);
  --bn-colors-selected-text: #ffffff;
  --bn-colors-selected-background: var(--semi-color-primary);
  --bn-colors-disabled-text: var(--semi-color-disabled-text);
  --bn-colors-disabled-background: var(--semi-color-disabled-bg);

  /* UI elements */
  --bn-colors-shadow: var(--semi-color-shadow);
  --bn-colors-border: var(--semi-color-border);
  --bn-colors-side-menu: rgba(255, 255, 255, 0.3);

  /* Other settings */
  --bn-border-radius: 6px;
}

/* Code block styling */
.bn-code-block {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  border-radius: 6px;
  overflow: hidden;
}

.bn-code-block pre {
  margin: 0;
  padding: 8px 12px;
  overflow-x: auto;
}

.bn-code-block code {
  font-size: 14px;
  line-height: 1.5;
}

/* Light theme code block */
.bn-container[data-color-scheme='light'] .bn-code-block {
  background-color: #f5f5f5;
  border: 1px solid var(--semi-color-border);
}

/* Dark theme code block */
.bn-container[data-color-scheme='dark'] .bn-code-block {
  background-color: #2d2d2d;
  border: 1px solid var(--semi-color-border);
}

/* Language selector styling */
.bn-code-block-language-selector {
  background-color: var(--bn-colors-menu-background);
  color: var(--bn-colors-menu-text);
  border: 1px solid var(--semi-color-border);
  border-radius: 4px;
  font-size: 13px;
}

/* Code block toolbar */
.bn-code-block-toolbar {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background-color: var(--bn-colors-menu-background);
  border-bottom: 1px solid var(--semi-color-border);
}

/* 自定义图片块样式，解决固定宽度512px的问题 */
.custom-blocknote-editor .bn-visual-media-wrapper {
  /* 移除固定宽度设置，但保留width属性以支持图片调整大小功能 */
  width: var(--media-width, auto) !important;
  max-width: 100% !important;
}

.custom-blocknote-editor .bn-visual-media {
  /* 图片使用原始尺寸 */
  width: auto !important;
  /* 但限制最大宽度，防止溢出 */
  max-width: 100% !important;
  /* 保持宽高比 */
  height: auto !important;
}

/* 确保调整大小后的尺寸能够保存 - 修改以兼容BlockNote内部机制 */
.custom-blocknote-editor .ProseMirror figure[data-image-resize] {
  /* 使用media-width变量但不强制覆盖BlockNote的内部机制 */
  max-width: 100% !important;
}

.custom-blocknote-editor .ProseMirror figure[data-image-resize] img {
  max-width: 100% !important;
  height: auto !important;
}

/* 确保拖动调整大小的控件正常工作 */
.custom-blocknote-editor .ProseMirror .media-resizer-handle {
  display: block !important;
}

/* 新增：支持BlockNote图片调整大小功能的样式 */
.custom-blocknote-editor .ProseMirror .bn-image-block {
  position: relative;
}

.custom-blocknote-editor .ProseMirror .bn-image-block img {
  max-width: 100% !important;
}

/* 支持图片调整大小操作 */
.custom-blocknote-editor .ProseMirror .media-resizer {
  display: block !important;
}

/* 新增：补充媒体调整大小相关的样式，解决ID查找问题 */
.custom-blocknote-editor .ProseMirror .resizable-media {
  position: relative;
  max-width: 100%;
}

/* 确保图片调整大小控件正确显示 */
.custom-blocknote-editor .ProseMirror .media-resizer .handle {
  position: absolute;
  display: block !important;
  z-index: 10;
}

/* 确保调整大小时图片宽度能够被正确保存 */
.custom-blocknote-editor .ProseMirror [data-node-view] {
  max-width: 100%;
}

.custom-blocknote-editor .ProseMirror [data-node-view='image'] {
  /* 确保节点视图宽度正确保存 */
  width: var(--media-width, auto);
}

/* 标签样式 */
.bn-tag {
  background-color: var(--semi-color-primary-light-default);
  color: var(--semi-color-primary);
  border-radius: 4px;
  padding: 0 6px;
  margin: 0 2px;
  font-weight: bold;
  user-select: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  transition: all 0.2s ease;
}

.bn-tag:hover {
  background-color: var(--semi-color-primary-light-hover);
}

/* 深色主题下的标签样式 */
.bn-dark .bn-tag {
  background-color: var(--semi-color-primary-light-active);
  color: var(--semi-color-primary-light);
}

/* 自定义AI回复窗口Popover样式 */
.ai-response-popover.semi-popover {
  z-index: 9999 !important;
  position: absolute !important;
  transform: translateZ(0); /* 创建新的堆叠上下文 */
  will-change: transform; /* 提高渲染层级 */
}

.ai-response-popover.semi-popover .semi-popover-content {
  max-width: 450px;
  min-width: 200px;
  overflow: visible;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999 !important; /* 确保内容也具有高层级 */
  transform: translateZ(0); /* 创建新的堆叠上下文 */
  will-change: transform; /* 提高渲染层级 */
}

.ai-popover-content {
  word-break: break-word;
  overflow-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
  position: relative;
  z-index: 9999; /* 确保与父容器相同的高z-index */
  transform: translateZ(0); /* 创建新的堆叠上下文 */
  will-change: transform; /* 提高渲染层级 */
}

/* 确保AI响应弹出窗口在不同容器内也能正确定位 */
.bn-container .ai-response-popover {
  position: relative;
  z-index: 9999 !important; /* 确保即使在BlockNote容器内也保持高层级 */
  transform: translateZ(0); /* 创建新的堆叠上下文 */
  will-change: transform; /* 提高渲染层级 */
}

/* 当编辑器宽度较小时，限制AI回复窗口的宽度 */
@media (max-width: 600px) {
  .ai-response-popover.semi-popover .semi-popover-content {
    max-width: 300px;
  }

  .ai-popover-content {
    max-height: 300px;
  }
}

/* 当编辑器宽度更小时，进一步限制AI回复窗口的宽度 */
@media (max-width: 400px) {
  .ai-response-popover.semi-popover .semi-popover-content {
    max-width: 250px;
  }

  .ai-popover-content {
    max-height: 250px;
  }
}

/* 确保Popover内容始终在可视区域内 */
.ai-response-popover.semi-popover .semi-popover-content:not(.semi-popover-content-hidden) {
  display: block !important;
  visibility: visible !important;
}

/* 防止超出边界 */
.bn-container {
  position: relative;
  overflow: visible;
}

/* 强制使Popover在视口内显示 */
.ai-response-popover.semi-popover[x-placement^='top'],
.ai-response-popover.semi-popover[x-placement^='bottom'],
.ai-response-popover.semi-popover[x-placement^='left'],
.ai-response-popover.semi-popover[x-placement^='right'] {
  visibility: visible !important;
}

/* BlockNote格式化工具栏样式 - 修复横向滚动条问题 */
.bn-container .bn-formatting-toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  width: 100%;
  overflow-x: visible;
  padding: 4px;
  z-index: 100; /* 确保低于popover的z-index */
}

/* 确保工具栏按钮在换行时有合适的间距 */
.bn-container .bn-formatting-toolbar-button {
  margin: 2px;
}

/* 确保工具栏按钮组容器也能换行 */
.bn-container .bn-formatting-toolbar-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/* 确保popover在任何情况下都显示在最上层 */
body .ai-response-popover.semi-popover,
.bn-container .ai-response-popover.semi-popover,
.custom-blocknote-editor .ai-response-popover.semi-popover {
  isolation: isolate; /* 创建独立的堆叠上下文 */
  position: absolute !important;
  z-index: 10000 !important; /* 使用更高的z-index */
}

/* 防止formatting toolbar换行时遮挡popover */
.bn-container .bn-formatting-toolbar {
  pointer-events: auto; /* 确保按钮可点击 */
}

/* 调整formatting toolbar的堆叠行为 */
.ai-response-popover.semi-popover {
  pointer-events: auto !important; /* 确保popover始终可以接收鼠标事件 */
}

.ai-status-ready {
  color: var(--semi-color-success);
  font-size: 16px;
}

.ai-status-error {
  color: var(--semi-color-danger);
  font-size: 16px;
}

/* 在AI下拉菜单后调整间距 */
.editor-right .semi-select {
  margin-left: -5px;
}
