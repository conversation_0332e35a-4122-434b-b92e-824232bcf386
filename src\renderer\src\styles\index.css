/* 导入自定义滚动条样式 */
@import url('./scrollbar.css');

body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans',
    'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Semi Design 暗黑模式适配 */
body[theme-mode='dark'] .semi-always-light {
  color-scheme: light;
}

body[theme-mode='light'] .semi-always-dark {
  color-scheme: dark;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

#root {
  width: 100%;
  height: 100vh;
  overflow: hidden; /* 防止滚动条影响布局 */
}

.components-layout-demo {
  height: 100vh; /* 使用视口高度确保完全覆盖 */
  width: 100vw; /* 使用视口宽度确保完全覆盖 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 导航栏样式 */
.resize-handle {
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background-color: var(--semi-color-primary-light-default) !important;
}

.secondary-nav {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 右键菜单样式 */
.context-menu-item {
  transition:
    background-color 0.2s,
    color 0.2s;
}

.context-menu-item:hover {
  background-color: var(--semi-color-primary-light-default);
}

.context-menu-item:hover svg {
  color: var(--semi-color-primary) !important;
}

.context-menu-item:hover .semi-typography {
  color: var(--semi-color-text-0) !important;
}

/* 删除按钮悬停时保持警告色 */
.context-menu-item:hover svg[data-icon='delete'] {
  color: var(--semi-color-danger) !important;
}

.context-menu-item:hover .semi-typography[style*='color: var(--semi-color-danger)'] {
  color: var(--semi-color-danger) !important;
}

/* 菜单分隔线 */
.context-menu-divider {
  height: 1px;
  background-color: var(--semi-color-border);
  margin: 4px 0;
}

/* 菜单危险操作项 */
.context-menu-item-danger:hover {
  background-color: var(--semi-color-danger-light-default) !important;
}

.context-menu {
  user-select: none;
}

/* 主题切换过渡效果 */
* {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* 全局增强文本选择样式 */
::selection {
  background-color: var(--semi-color-primary-light-hover) !important;
  /* 选中高亮透明度 */
  opacity: 0.8 !important;
}

/* 确保 semi-portal-inner 显示在 data-floating-ui-focusable 的上层 */
.semi-portal-inner {
  z-index: 10050 !important; /* 设置一个比已有最高值 10000 更高的值 */
  isolation: isolate; /* 创建独立的堆叠上下文 */
  width: auto !important; /* 确保不会覆盖原有宽度 */
  height: auto !important; /* 确保不会覆盖原有高度 */
  max-width: none !important; /* 不限制最大宽度 */
  max-height: none !important; /* 不限制最大高度 */
  transform: none !important; /* 移除可能影响尺寸的变换 */
  will-change: auto; /* 使用默认值，避免创建额外的渲染层 */
}

/* 当 semi-portal-inner 中包含弹出内容时，确保更高的优先级，但不影响尺寸 */
.semi-portal-inner .semi-popover-content,
.semi-portal-inner .semi-modal-content,
.semi-portal-inner .semi-dropdown-menu {
  z-index: 10051 !important;
  position: relative;
}

/* Semi Design Select下拉菜单优化 - 简化版本 */
.semi-select-dropdown {
  z-index: 10052 !important;
}

/* 针对 data-floating-ui-focusable 元素调整 z-index，确保不会遮挡弹出层 */
[data-floating-ui-focusable] {
  z-index: 1000 !important; /* 设置一个比 semi-portal-inner 低的值 */
}

/* 骨架屏全局样式增强 */
.skeleton-wrapper {
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

/* 骨架屏在暗色主题下的适配 */
body[theme-mode='dark'] .skeleton-wrapper {
  filter: brightness(0.9);
}

/* 确保骨架屏组件在加载时平滑过渡 */
.editor-skeleton,
.navigation-skeleton,
.data-analysis-skeleton,
.chart-skeleton {
  transition: opacity 0.3s ease-in-out;
}

/* 骨架屏加载完成后的淡出效果 */
.skeleton-fade-out {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

/* AI对话页面Chat组件样式优化 */
.chat-interface-container .semi-chat {
  max-width: none !important;
  width: 100% !important;
}

.chat-interface-container .semi-chat-content {
  max-width: none !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

.chat-interface-container .semi-chat-list {
  max-width: none !important;
  width: 100% !important;
  padding: 0 !important;
}

.chat-interface-container .semi-chat-input-area {
  max-width: none !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 更具体的Semi Chat样式覆盖 */
.chat-interface-container .semi-chat-wrapper {
  max-width: none !important;
  width: 100% !important;
}

.chat-interface-container .semi-chat-container {
  max-width: none !important;
  width: 100% !important;
}

/* 覆盖Semi Design Chat组件的默认800px最大宽度限制 */
.semi-chat {
  max-width: none !important;
}

.semi-chat-content {
  max-width: none !important;
}

.semi-chat-list {
  max-width: none !important;
}

.semi-chat-input-area {
  max-width: none !important;
}
